"use client";

import React from "react";
import { SlidersHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";

interface ColumnActionsProps<T extends Record<string, boolean>> {
  columns: (keyof T)[];
  columnVisibility: T;
  onColumnVisibilityChange: (visibility: T) => void;
  columnLabels: Record<keyof T, string>;
}

export function ColumnActions<T extends Record<string, boolean>>({
  columns,
  columnVisibility,
  onColumnVisibilityChange,
  columnLabels,
}: ColumnActionsProps<T>) {
  const handleColumnToggle = (column: keyof T, checked: boolean) => {
    onColumnVisibilityChange({
      ...columnVisibility,
      [column]: checked,
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-9">
          <SlidersHorizontal className="mr-2 h-4 w-4" />
          Kolom
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Tampilkan Kolom</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <div className="max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
          {columns.map((column) => (
            <DropdownMenuCheckboxItem
              key={String(column)}
              checked={columnVisibility[column]}
              onCheckedChange={(checked) =>
                handleColumnToggle(column, !!checked)
              }
              onSelect={(e) => e.preventDefault()}
            >
              {columnLabels[column]}
            </DropdownMenuCheckboxItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

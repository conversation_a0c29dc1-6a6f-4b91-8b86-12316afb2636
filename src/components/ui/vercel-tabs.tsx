"use client";

import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface Tab {
  id: string;
  label: string;
}

interface TabsProps extends React.HTMLAttributes<HTMLDivElement> {
  tabs: Tab[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
}

const Tabs = React.forwardRef<HTMLDivElement, TabsProps>(
  ({ className, tabs, activeTab, onTabChange, ...props }, ref) => {
    // hoveredIndex will now be for the *underline* on non-active tabs
    const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
    // activeIndex will control the full blue background for the active tab
    const [activeIndex, setActiveIndex] = useState(
      activeTab ? tabs.findIndex((tab) => tab.id === activeTab) : 0
    );

    const [activeHighlightStyle, setActiveHighlightStyle] = useState({
      left: "0px",
      width: "0px",
    });
    const [hoverUnderlineStyle, setHoverUnderlineStyle] = useState({
      left: "0px",
      width: "0px",
    });
    const tabRefs = useRef<(HTMLDivElement | null)[]>([]);

    // Effect for updating the active tab's background highlight
    useEffect(() => {
      const currentActiveIndex = activeTab
        ? tabs.findIndex((tab) => tab.id === activeTab)
        : 0;
      setActiveIndex(currentActiveIndex);
    }, [activeTab, tabs]);

    useEffect(() => {
      const activeElement = tabRefs.current[activeIndex];
      if (activeElement) {
        const { offsetLeft, offsetWidth } = activeElement;
        setActiveHighlightStyle({
          left: `${offsetLeft}px`,
          width: `${offsetWidth}px`,
        });
      }
    }, [activeIndex, tabs.length]); // Re-run if activeIndex changes or tabs array length changes

    // Effect for updating the hover underline on non-active tabs
    useEffect(() => {
      if (hoveredIndex !== null && hoveredIndex !== activeIndex) {
        const hoveredElement = tabRefs.current[hoveredIndex];
        if (hoveredElement) {
          const { offsetLeft, offsetWidth } = hoveredElement;
          setHoverUnderlineStyle({
            left: `${offsetLeft}px`,
            width: `${offsetWidth}px`,
          });
        }
      } else {
        // If not hovering or hovering over the active tab, hide the underline
        setHoverUnderlineStyle({ left: "0px", width: "0px" });
      }
    }, [hoveredIndex, activeIndex]);

    // Initialize active tab highlight on mount
    useEffect(() => {
      requestAnimationFrame(() => {
        const initialActiveElement = tabRefs.current[activeIndex];
        if (initialActiveElement) {
          const { offsetLeft, offsetWidth } = initialActiveElement;
          setActiveHighlightStyle({
            left: `${offsetLeft}px`,
            width: `${offsetWidth}px`,
          });
        }
      });
    }, [activeIndex]);

    return (
      <div ref={ref} className={cn("relative", className)} {...props}>
        <div className="relative">
          {/* Active Tab Background Highlight (persists) */}
          <div
            className="absolute h-[40px] md:h-[40px] sm:h-[36px] transition-all duration-300 ease-out bg-blue-500 dark:bg-[#ffffff1a] rounded-[6px] flex items-center"
            style={activeHighlightStyle}
          />

          {/* Hover Underline (for non-active tabs) */}
          <div
            className="absolute bottom-[-6px] h-[2px] bg-blue-500 dark:bg-white transition-all duration-300 ease-out"
            style={{
              ...hoverUnderlineStyle,
              opacity:
                hoveredIndex !== null && hoveredIndex !== activeIndex ? 1 : 0,
            }}
          />

          {/* Tabs */}
          <div className="relative flex gap-x-[6px] items-center w-full">
            {tabs.map((tab, index) => (
              <div
                key={tab.id}
                ref={(el) => {
                  tabRefs.current[index] = el;
                }}
                className={cn(
                  "px-2 md:px-3 py-1 md:py-2 cursor-pointer transition-colors duration-300 h-[36px] md:h-[40px] flex-1 text-center",
                  // Text color for active tab
                  index === activeIndex
                    ? "text-white" // Active tab always white text
                    : // Text color for non-active tabs, changes on hover
                      hoveredIndex === index
                      ? "text-gray-700"
                      : "text-gray-700 dark:text-[#ffffff99]"
                )}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
                onClick={() => {
                  setActiveIndex(index);
                  onTabChange?.(tab.id);
                }}
              >
                <div className="text-sm md:text-base font-semibold leading-4 md:leading-5 whitespace-nowrap flex items-center justify-center h-full">
                  {tab.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
);
Tabs.displayName = "Tabs";

export { Tabs };
